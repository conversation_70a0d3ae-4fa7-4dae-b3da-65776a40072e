'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  Modal,
  Button,
  Group,
  Text,
  Stack,
  TextInput,
  Textarea,
  Select,
  Paper,
  Image,
  ActionIcon,
  Progress,
  Grid,
  Card,
  Box
} from '@mantine/core';
import { Dropzone, IMAGE_MIME_TYPE } from '@mantine/dropzone';
import { DatePickerInput } from '@mantine/dates';
import { notifications } from '@mantine/notifications';
import { IconUpload, IconX, IconPhoto, IconTrash, IconTemplate, IconPlus } from '@tabler/icons-react';
import { fetchData, updateData, uploadFile, getFileUrl } from '../../lib/supabase';
import {
  ASSET_TYPES,
  AssetType,
  AssetTemplate,
  validateAssetData,
  createDefaultAssetMetadata,
  generateAssetFilename,
  Asset
} from '../../lib/assets-utils';
import AssetDisplay from './AssetDisplay';

interface AssetEditFormProps {
  asset: Asset | null;
  opened: boolean;
  onClose: () => void;
  onAssetUpdated?: () => void;
}

interface FormData {
  title: string;
  description: string;
  asset_type: AssetType | '';
  template_id: string;
  expiry_date: Date | null;
}

export function AssetEditForm({ asset, opened, onClose, onAssetUpdated }: AssetEditFormProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [templates, setTemplates] = useState<AssetTemplate[]>([]);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<AssetTemplate | null>(null);
  
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    asset_type: '',
    template_id: '',
    expiry_date: null
  });

  // Initialize form data when asset changes
  useEffect(() => {
    if (asset && opened) {
      setFormData({
        title: asset.title || '',
        description: asset.description || '',
        asset_type: asset.asset_type || '',
        template_id: asset.template_id || '',
        expiry_date: asset.expiry_date ? new Date(asset.expiry_date) : null
      });
      setImagePreview(asset.image_url || null);
      fetchTemplates();
    }
  }, [asset, opened]);

  // Fetch templates on component mount
  useEffect(() => {
    if (opened) {
      fetchTemplates();
    }
  }, [opened]);

  const fetchTemplates = async () => {
    try {
      const { data, error } = await fetchData('asset_templates', {
        select: '*',
        filter: [{ column: 'is_active', value: true }]
      });

      if (!error && data) {
        const templatesArray = Array.isArray(data) ? data : [data];
        setTemplates(templatesArray);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const handleImageDrop = (files: File[]) => {
    const file = files[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(asset?.image_url || null);
  };

  const handleTemplateChange = (templateId: string | null) => {
    setFormData(prev => ({ ...prev, template_id: templateId || '' }));
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplate(template || null);
  };

  const handleSubmit = async () => {
    if (!asset) return;

    // Basic validation
    if (!formData.title.trim()) {
      notifications.show({
        title: 'Validation Error',
        message: 'Asset title is required',
        color: 'red',
      });
      return;
    }

    if (!formData.asset_type) {
      notifications.show({
        title: 'Validation Error',
        message: 'Asset type is required',
        color: 'red',
      });
      return;
    }

    try {
      setLoading(true);
      setUploading(true);
      setUploadProgress(0);

      let imageUrl = asset.image_url;

      // Upload new image if selected
      if (selectedImage) {
        setUploadProgress(25);
        const filename = generateAssetFilename(formData.title, formData.asset_type as AssetType);
        const filePath = `assets/${filename}.${selectedImage.name.split('.').pop()}`;

        const { error: uploadError } = await uploadFile('images', filePath, selectedImage);
        if (uploadError) throw uploadError;

        setUploadProgress(50);
        imageUrl = getFileUrl('images', filePath);
      }

      setUploadProgress(75);

      const expiryDateISO = formData.expiry_date ? formData.expiry_date.toISOString() : null;

      const updateData = {
        title: formData.title.trim(),
        description: formData.description.trim() || null,
        asset_type: formData.asset_type as AssetType,
        template_id: formData.template_id || null,
        expiry_date: expiryDateISO,
        image_url: imageUrl,
        metadata: createDefaultAssetMetadata(formData.asset_type as AssetType)
      };

      // Update asset using API
      const response = await fetch('/api/assets', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: asset.id,
          ...updateData
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update asset');
      }

      setUploadProgress(100);

      notifications.show({
        title: 'Success',
        message: 'Asset updated successfully',
        color: 'green',
      });

      if (onAssetUpdated) {
        onAssetUpdated();
      }
      
      onClose();

    } catch (error) {
      console.error('Error updating asset:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to update asset',
        color: 'red',
      });
    } finally {
      setLoading(false);
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setSelectedImage(null);
      setImagePreview(null);
      setUploadProgress(0);
      onClose();
    }
  };

  if (!asset) return null;

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Edit Asset"
      size="lg"
      centered
      closeOnClickOutside={!loading}
      closeOnEscape={!loading}
    >
      <Stack gap="md">
        {/* Basic Information */}
        <Grid>
          <Grid.Col span={12}>
            <TextInput
              label="Asset Title"
              placeholder="Enter asset title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              required
              disabled={loading}
            />
          </Grid.Col>
          
          <Grid.Col span={6}>
            <Select
              label="Asset Type"
              placeholder="Select asset type"
              data={ASSET_TYPES.map(type => ({ value: type, label: type }))}
              value={formData.asset_type}
              onChange={(value) => setFormData(prev => ({ ...prev, asset_type: value as AssetType || '' }))}
              required
              disabled={loading}
            />
          </Grid.Col>

          <Grid.Col span={6}>
            <DatePickerInput
              label="Expiry Date (Optional)"
              placeholder="Select expiry date"
              value={formData.expiry_date}
              onChange={(date) => setFormData(prev => ({ ...prev, expiry_date: date as Date | null }))}
              clearable
              disabled={loading}
            />
          </Grid.Col>
        </Grid>

        <Textarea
          label="Description (Optional)"
          placeholder="Enter asset description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          minRows={3}
          disabled={loading}
        />

        {/* Template Selection */}
        <Select
          label="Template (Optional)"
          placeholder="Select a template"
          data={templates.map(template => ({
            value: template.id,
            label: `${template.name} (${template.asset_type})`
          }))}
          value={formData.template_id}
          onChange={handleTemplateChange}
          disabled={loading}
        />

        {/* Image Upload */}
        <div>
          <Text size="sm" fw={500} mb="xs">Asset Image</Text>
          {imagePreview ? (
            <Paper withBorder p="md">
              <Group justify="space-between" mb="md">
                <Text size="sm">Current Image</Text>
                <ActionIcon
                  color="red"
                  variant="light"
                  onClick={removeImage}
                  disabled={loading}
                >
                  <IconTrash size={16} />
                </ActionIcon>
              </Group>
              <AssetDisplay
                imageUrl={imagePreview}
                templateId={formData.template_id}
                title={formData.title || 'Asset preview'}
                height={200}
                fit="contain"
              />
            </Paper>
          ) : (
            <Dropzone
              onDrop={handleImageDrop}
              accept={IMAGE_MIME_TYPE}
              maxFiles={1}
              disabled={loading}
            >
              <Group justify="center" gap="xl" mih={220} style={{ pointerEvents: 'none' }}>
                <Dropzone.Accept>
                  <IconUpload size={52} stroke={1.5} />
                </Dropzone.Accept>
                <Dropzone.Reject>
                  <IconX size={52} stroke={1.5} />
                </Dropzone.Reject>
                <Dropzone.Idle>
                  <IconPhoto size={52} stroke={1.5} />
                </Dropzone.Idle>

                <div>
                  <Text size="xl" inline>
                    Drag image here or click to select
                  </Text>
                  <Text size="sm" c="dimmed" inline mt={7}>
                    Upload a new image for this asset
                  </Text>
                </div>
              </Group>
            </Dropzone>
          )}
        </div>

        {/* Upload Progress */}
        {uploading && (
          <Box>
            <Text size="sm" mb="xs">Updating asset...</Text>
            <Progress value={uploadProgress} animated />
          </Box>
        )}

        {/* Action Buttons */}
        <Group justify="flex-end">
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} loading={loading}>
            Update Asset
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
